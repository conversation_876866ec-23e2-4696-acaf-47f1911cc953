package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/Unleash/unleash-client-go/v4"
	unleashContext "github.com/Unleash/unleash-client-go/v4/context"
	"github.com/google/uuid"
)

const (
	deviceCookie = "device_id"
)

// Config holds all application configuration
type Config struct {
	ExperimentName     string
	ExperimentGroupA   string
	ExperimentGroupB   string
	ExperimentEventHit string
	SoguURL            string
	ProxyURL           string
	RedirectURL        string
	UnleashURL         string
	UnleashAPIKey      string
	UnleashAppName     string
	UnleashFeature     string
	ServiceTarget      *url.URL
	AppVersion         string
	BrowserDebugLogs   bool
	Port               string
	ShutdownTimeout    time.Duration
	HTTPTimeout        time.Duration
}

// App holds application state
type App struct {
	config        *Config
	httpClient    *http.Client
	logger        *slog.Logger
	server        *http.Server
	unleashClient *unleash.Client
	startTime     time.Time
}

var (
	AppVersion = "dev" // Will be set during build
)

func main() {
	// Initialize structured logger
	logger := slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))

	// Load configuration
	config, err := loadConfig()
	if err != nil {
		logger.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	// Initialize Unleash client
	unleashClient, err := unleash.NewClient(
		unleash.WithUrl(config.UnleashURL),
		unleash.WithInstanceId(config.UnleashAPIKey),
		unleash.WithAppName(config.UnleashAppName),
		unleash.WithRefreshInterval(15*time.Second),
	)
	if err != nil {
		logger.Error("Failed to initialize Unleash client", "error", err)
		os.Exit(1)
	}

	// Create application instance
	app := &App{
		config: config,
		httpClient: &http.Client{
			Timeout: config.HTTPTimeout,
		},
		logger:        logger,
		unleashClient: unleashClient,
		startTime:     time.Now(),
	}

	// Setup HTTP server with timeouts
	mux := http.NewServeMux()
	mux.HandleFunc("/", app.loggingMiddleware(app.splitHandler))
	mux.HandleFunc("/health", app.healthHandler)

	app.server = &http.Server{
		Addr:         ":" + config.Port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Start server in goroutine
	go func() {
		logger.Info("Starting splitter server", "port", config.Port, "version", AppVersion)
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("Server failed to start", "error", err)
			os.Exit(1)
		}
	}()

	// Wait for interrupt signal for graceful shutdown
	app.gracefulShutdown()
}

// loadConfig loads configuration from environment variables
func loadConfig() (*Config, error) {
	config := &Config{
		ExperimentName:     getenv("EXPERIMENT_NAME", "monolit-mf-ab-test"),
		ExperimentGroupA:   getenv("EXPERIMENT_GROUP_A", "Monolit"),
		ExperimentGroupB:   getenv("EXPERIMENT_GROUP_B", "MF"),
		ExperimentEventHit: getenv("EXPERIMENT_EVENT_HIT", "experiments.hit"),
		RedirectURL:        getenv("REDIRECT_URL", "https://google.com"),
		ProxyURL:           getenv("PROXY_URL", "https://ya.ru"),
		SoguURL:            getenv("SOGU_URL", "https://sogu-staging.sogu.dev.tripster.tech/events/"),
		UnleashURL:         getenv("UNLEASH_URL", ""),
		UnleashAPIKey:      getenv("UNLEASH_API_KEY", ""),
		UnleashAppName:     getenv("UNLEASH_APP_NAME", "splitter"),
		UnleashFeature:     getenv("UNLEASH_FEATURE", "monolit-mf-ab-test"),
		Port:               getenv("PORT", "8080"),
		AppVersion:         AppVersion,
		ShutdownTimeout:    30 * time.Second,
		HTTPTimeout:        10 * time.Second,
	}

	// Parse boolean values
	browserDebugLogsStr := getenv("BROWSER_DEBUG_LOGS", "false")
	browserDebugLogs, err := strconv.ParseBool(browserDebugLogsStr)
	if err != nil {
		return nil, fmt.Errorf("invalid BROWSER_DEBUG_LOGS: %v", err)
	}
	config.BrowserDebugLogs = browserDebugLogs

	// Parse service target URL
	serviceTarget, err := url.Parse(config.ProxyURL)
	if err != nil {
		return nil, fmt.Errorf("invalid PROXY_URL: %v", err)
	}
	config.ServiceTarget = serviceTarget

	return config, nil
}

// gracefulShutdown handles graceful shutdown of the application
func (app *App) gracefulShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	app.logger.Info("Shutting down server...")

	// Close Unleash client
	app.unleashClient.Close()

	ctx, cancel := context.WithTimeout(context.Background(), app.config.ShutdownTimeout)
	defer cancel()

	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("Server forced to shutdown", "error", err)
		os.Exit(1)
	}

	app.logger.Info("Server exited")
}

// Middleware functions
func (app *App) loggingMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Create a response writer wrapper to capture status code
		ww := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		next.ServeHTTP(ww, r)

		duration := time.Since(start)
		app.logger.Info("Request processed",
			"method", r.Method,
			"path", r.URL.Path,
			"status", ww.statusCode,
			"duration_ms", duration.Milliseconds(),
			"user_agent", r.Header.Get("User-Agent"),
		)
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// splitHandler handles the main A/B testing logic
func (app *App) splitHandler(w http.ResponseWriter, r *http.Request) {
	deviceID := app.getOrSetDeviceID(w, r)

	// Create Unleash context with user ID
	unleashCtx := &unleashContext.Context{
		UserId: deviceID,
	}

	// Check if feature is enabled for this user
	//enabled := app.unleashClient.IsEnabled(app.config.UnleashFeature, unleash.WithContext(*unleashCtx))
	variant := app.unleashClient.GetVariant(app.config.UnleashFeature, unleash.WithVariantContext(*unleashCtx))

	// Determine group based on Unleash decision
	var group string
	if variant.FeatureEnabled {
		group = app.config.ExperimentGroupB
	} else {
		group = app.config.ExperimentGroupA
	}

	// Send analytics asynchronously
	go app.sendAnalytics(r, deviceID, group)

	// Redirect group B to different URL
	if group == app.config.ExperimentGroupB {
		http.Redirect(w, r, app.config.RedirectURL, http.StatusFound)
		return
	}

	// Proxy to the target service
	proxy := httputil.NewSingleHostReverseProxy(app.config.ServiceTarget)
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		app.logger.Error("Proxy error", "error", err, "url", r.URL.String())
		http.Error(w, "Service temporarily unavailable", http.StatusBadGateway)
	}
	proxy.ServeHTTP(w, r)
}

func (app *App) getCookie(r *http.Request, name string) string {
	cookie, err := r.Cookie(name)
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}
	return ""
}

func (app *App) getOrSetDeviceID(w http.ResponseWriter, r *http.Request) string {
	cookieValue := app.getCookie(r, deviceCookie)
	if cookieValue != "" {
		return cookieValue
	}

	newID := uuid.New().String()
	http.SetCookie(w, &http.Cookie{
		Name:     deviceCookie,
		Value:    newID,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		MaxAge:   30 * 24 * 3600, // 30 days
	})
	return newID
}

func (app *App) sendAnalytics(r *http.Request, deviceID, group string) {
	if app.config.SoguURL == "" {
		return
	}

	gaClientID := app.getCookie(r, "_ga")
	yandexClientID := app.getCookie(r, "_ym_uid")

	type Params struct {
		DeviceID     string `json:"device_id"`
		Experiment   string `json:"experiment"`
		Variant      string `json:"variant"`
		WasGenerated bool   `json:"was_generated"`
	}

	type Event struct {
		AppVersion       string `json:"app_version"`
		EventName        string `json:"event_name"`
		Platform         string `json:"platform"`
		URL              string `json:"url"`
		BrowserDebugLogs bool   `json:"browser_debug_logs"`
		GAClientID       string `json:"ga_client_id"`
		YAClientID       string `json:"ya_client_id"`
		UserAgent        string `json:"user_agent"`
		DeviceID         string `json:"device_id"`
		DT               int64  `json:"dt"`
		Params           Params `json:"params"`
	}

	event := Event{
		AppVersion:       app.config.AppVersion,
		EventName:        app.config.ExperimentEventHit,
		Platform:         "web",
		URL:              r.URL.String(),
		BrowserDebugLogs: app.config.BrowserDebugLogs,
		GAClientID:       gaClientID,
		YAClientID:       yandexClientID,
		UserAgent:        r.Header.Get("User-Agent"),
		DeviceID:         deviceID,
		DT:               time.Now().Unix(),
		Params: Params{
			DeviceID:     deviceID,
			Experiment:   app.config.ExperimentName,
			Variant:      group,
			WasGenerated: false,
		},
	}

	body, err := json.Marshal([]Event{event})
	if err != nil {
		app.logger.Error("Failed to marshal analytics payload", "error", err)
		return
	}

	app.logger.Debug("Sending analytics payload", "body", string(body))
	app.logger.Debug("Sending analytics event", "device_id", deviceID, "group", group)

	// Асинхронная отправка с использованием HTTP клиента с таймаутом
	//go func() {
	//	resp, err := app.httpClient.Post(app.config.SoguURL, "application/json", bytes.NewBuffer(body))
	//	if err != nil {
	//		app.logger.Error("Failed to send analytics", "error", err)
	//		return
	//	}
	//	defer resp.Body.Close()
	//
	//	if resp.StatusCode >= 400 {
	//		app.logger.Error("Analytics request failed", "status_code", resp.StatusCode)
	//	} else {
	//		app.logger.Debug("Analytics sent successfully", "device_id", deviceID, "group", group)
	//	}
	//}()
}

// Health check handlers
func (app *App) healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"status":  "healthy",
		"version": app.config.AppVersion,
		"time":    time.Now().UTC().Format(time.RFC3339),
	}

	json.NewEncoder(w).Encode(response)
}

func getenv(key, fallback string) string {
	val := os.Getenv(key)
	if val == "" {
		return fallback
	}
	return val
}
